<!-- Learn how to maintain this file at https://github.com/WordPress/gutenberg/tree/HEAD/packages#maintaining-changelogs. -->

## Unreleased

## 1.30.0 (2025-09-03)

## 1.29.0 (2025-08-20)

## 1.28.0 (2025-08-07)

## 1.27.0 (2025-07-23)

## 1.26.0 (2025-06-25)

## 1.25.0 (2025-06-04)

## 1.24.0 (2025-05-22)

## 1.23.0 (2025-05-07)

## 1.22.0 (2025-04-11)

### Enhancements

* Added new `PageUtils.emulateNetworkConditions()` helper to emulate network conditions such as Slow 3G, Fast 4G or Broadband ([#69865](https://github.com/WordPress/gutenberg/pull/69865)).

## 1.21.0 (2025-03-27)

## 1.20.0 (2025-03-13)

## 1.19.0 (2025-02-28)

## 1.18.0 (2025-02-12)

## 1.17.0 (2025-01-29)

## 1.16.0 (2025-01-15)

## 1.15.0 (2025-01-02)

## 1.14.0 (2024-12-11)

## 1.13.0 (2024-11-27)

## 1.12.0 (2024-11-16)

## 1.11.0 (2024-10-30)

## 1.10.0 (2024-10-16)

## 1.9.0 (2024-10-03)

## 1.8.0 (2024-09-19)

## 1.7.0 (2024-09-05)

## 1.6.0 (2024-08-21)

## 1.5.0 (2024-08-07)

## 1.4.0 (2024-07-24)

## 1.3.0 (2024-07-10)

## 1.2.0 (2024-06-26)

## 1.1.0 (2024-06-15)

## 1.0.0 (2024-05-31)

### Breaking Changes

-   Increase the minimum required Node.js version to v18.12.0 matching long-term support releases ([#31270](https://github.com/WordPress/gutenberg/pull/61930)). Learn more about [Node.js releases](https://nodejs.org/en/about/previous-releases).

## 0.26.0 (2024-05-16)

## 0.25.0 (2024-05-02)

## 0.24.0 (2024-04-19)

## 0.23.0 (2024-04-03)

## 0.22.0 (2024-03-21)

## 0.21.0 (2024-03-06)

## 0.20.0 (2024-02-21)

## 0.19.0 (2024-02-09)

## 0.18.0 (2024-01-24)

## 0.17.0 (2024-01-10)

## 0.16.0 (2023-12-13)

## 0.15.0 (2023-11-29)

## 0.14.0 (2023-11-16)

## 0.13.0 (2023-11-02)

## 0.12.0 (2023-10-18)

## 0.11.0 (2023-10-05)

## 0.10.0 (2023-09-20)

## 0.9.0 (2023-08-31)

## 0.8.0 (2023-08-16)

## 0.7.0 (2023-08-10)

## 0.6.0 (2023-07-20)

## 0.5.0 (2023-07-05)

## 0.4.0 (2023-06-23)

## 0.3.0 (2023-06-07)

## 0.2.0 (2023-05-24)

## 0.1.0 (2023-05-10)

-   Initial version of the package.
