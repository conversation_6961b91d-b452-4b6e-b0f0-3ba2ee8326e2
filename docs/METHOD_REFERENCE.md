# WordPress E2E Test Utils Playwright - Method Reference

## Admin Class Methods

### Navigation Methods

#### `createNewPost(options?: NewPostOptions): Promise<void>`
Creates a new post and navigates to the editor.

**Parameters:**
- `options.postType?: string` - Post type (default: 'post')
- `options.title?: string` - Pre-fill post title
- `options.content?: string` - Pre-fill post content
- `options.excerpt?: string` - Pre-fill post excerpt
- `options.showWelcomeGuide?: boolean` - Show welcome guide (default: false)
- `options.fullscreenMode?: boolean` - Enable fullscreen mode (default: false)

**Example:**
```javascript
await admin.createNewPost({
  postType: 'page',
  title: 'New Page',
  showWelcomeGuide: false
});
```

#### `editPost(postId: number): Promise<void>`
Navigate to edit an existing post by ID.

#### `visitAdminPage(adminPath: string, query?: string): Promise<void>`
Navigate to any WordPress admin page.

**Parameters:**
- `adminPath: string` - Admin page path (e.g., 'options-general.php')
- `query?: string` - URL query parameters

#### `visitSiteEditor(query?: URLSearchParams): Promise<void>`
Navigate to the Site Editor with optional query parameters.

#### `getPageError(): Promise<string | null>`
Get any page error that occurred during navigation.

## Editor Class Methods

### Content Management

#### `insertBlock(blockRepresentation: BlockRepresentation, options?: InsertBlockOptions): Promise<void>`
Insert a block into the editor.

**BlockRepresentation:**
```typescript
interface BlockRepresentation {
  name: string;
  attributes?: Record<string, any>;
  innerBlocks?: BlockRepresentation[];
}
```

**Options:**
- `clientId?: string` - Parent block client ID to insert into

#### `setContent(content: string): Promise<void>`
Set the entire post content as HTML.

#### `getEditedPostContent(): Promise<string>`
Get the current post content as HTML.

#### `getBlocks(): Promise<Block[]>`
Get all blocks in the editor as an array of block objects.

### Block Selection and Manipulation

#### `selectBlocks(startClientId: string, endClientId?: string): Promise<void>`
Select one or more blocks by client ID.

#### `clickBlockToolbarButton(label: string): Promise<void>`
Click a button in the block toolbar by label.

#### `clickBlockOptionsMenuItem(label: string): Promise<void>`
Click an item in the block options menu (three dots menu).

#### `showBlockToolbar(): Promise<void>`
Show the block toolbar for the selected block.

#### `transformBlockTo(name: string): Promise<void>`
Transform the selected block to another block type.

### Editor Interface

#### `openDocumentSettingsSidebar(): Promise<void>`
Open the document settings sidebar.

#### `setIsFixedToolbar(isFixed: boolean): Promise<void>`
Set whether the block toolbar is fixed at the top.

#### `switchEditorTool(tool: string): Promise<void>`
Switch to a different editor tool ('edit', 'select', etc.).

#### `switchToLegacyCanvas(): Promise<void>`
Switch to legacy canvas mode.

### Publishing and Saving

#### `publishPost(): Promise<void>`
Publish the current post.

#### `saveDraft(): Promise<void>`
Save the current post as a draft.

#### `openPreviewPage(): Promise<Page>`
Open the post preview in a new page and return the page object.

### Preferences and Settings

#### `setPreferences(scope: string, preferences: Record<string, any>): Promise<void>`
Set editor preferences for a specific scope.

**Example:**
```javascript
await editor.setPreferences('core/edit-post', {
  welcomeGuide: false,
  fullscreenMode: true,
  fixedToolbar: false
});
```

### Site Editor

#### `saveSiteEditorEntities(): Promise<void>`
Save all modified entities in the Site Editor.

## PageUtils Class Methods

### Keyboard Interactions

#### `pressKeys(key: string, options?: KeyPressOptions): Promise<void>`
Press keyboard keys with support for modifier combinations.

**Key Modifiers:**
- `primary` - Cmd on Mac, Ctrl on others
- `primaryShift` - Cmd+Shift on Mac, Ctrl+Shift on others
- `primaryAlt` - Cmd+Alt on Mac, Ctrl+Alt on others
- `secondary` - Shift+Alt+Cmd on Mac, Shift+Alt+Ctrl on others
- `access` - Ctrl+Alt on Mac, Alt on others
- `shift`, `alt`, `ctrl` - Direct modifiers

**Options:**
- `times?: number` - Number of times to press the key
- `delay?: number` - Delay between key presses (when times > 1)

**Examples:**
```javascript
await pageUtils.pressKeys('primary+a'); // Select all
await pageUtils.pressKeys('Enter', { times: 3 }); // Press Enter 3 times
await pageUtils.pressKeys('primary+shift+k'); // Insert link
```

### Clipboard Operations

#### `setClipboardData(data: ClipboardData): void`
Set clipboard data for paste operations.

**ClipboardData:**
```typescript
interface ClipboardData {
  plainText?: string;
  html?: string;
}
```

### File Operations

#### `dragFiles(selector: string, files: string[]): Promise<void>`
Drag and drop files onto an element.

**Parameters:**
- `selector: string` - CSS selector for the drop target
- `files: string[]` - Array of file paths to drag

### Navigation Utilities

#### `isCurrentURL(expectedURL: string): Promise<boolean>`
Check if the current URL matches the expected URL.

### Browser Configuration

#### `setBrowserViewport(viewport: Viewport): Promise<void>`
Set the browser viewport size.

**Viewport:**
```typescript
interface Viewport {
  width: number;
  height: number;
}
```

#### `emulateNetworkConditions(conditions: NetworkConditions): Promise<void>`
Emulate network conditions for testing.

## RequestUtils Class Methods

### Authentication

#### `static setup(options: SetupOptions): Promise<RequestUtils>`
Create and configure a RequestUtils instance.

**SetupOptions:**
```typescript
interface SetupOptions {
  user?: User;
  storageStatePath?: string;
  baseURL?: string;
}
```

#### `login(): Promise<void>`
Authenticate with WordPress and store session.

#### `setupRest(): Promise<void>`
Setup REST API authentication with nonce.

### Content Management - Posts

#### `createPost(payload: CreatePostPayload): Promise<Post>`
Create a new post via REST API.

**CreatePostPayload:**
```typescript
interface CreatePostPayload {
  title?: string;
  content?: string;
  status: 'publish' | 'draft' | 'pending' | 'private';
  date?: string;
  date_gmt?: string;
  excerpt?: string;
  featured_media?: number;
  categories?: number[];
  tags?: number[];
}
```

#### `deleteAllPosts(): Promise<void>`
Delete all posts (all statuses including trash).

### Content Management - Pages

#### `createPage(payload: CreatePagePayload): Promise<Page>`
Create a new page via REST API.

#### `deleteAllPages(): Promise<void>`
Delete all pages.

### Content Management - Comments

#### `createComment(payload: CreateCommentPayload): Promise<Comment>`
Create a new comment.

**CreateCommentPayload:**
```typescript
interface CreateCommentPayload {
  post: number;
  content: string;
  author_name?: string;
  author_email?: string;
  author_url?: string;
  parent?: number;
}
```

#### `deleteAllComments(): Promise<void>`
Delete all comments.

### Media Management

#### `uploadMedia(filePath: string): Promise<Media>`
Upload a media file.

#### `listMedia(params?: MediaListParams): Promise<Media[]>`
List media files.

#### `deleteMedia(mediaId: number): Promise<void>`
Delete a specific media file.

#### `deleteAllMedia(): Promise<void>`
Delete all media files.

### User Management

#### `createUser(payload: CreateUserPayload): Promise<User>`
Create a new user.

**CreateUserPayload:**
```typescript
interface CreateUserPayload {
  username: string;
  email: string;
  password: string;
  first_name?: string;
  last_name?: string;
  roles?: string[];
}
```

#### `deleteAllUsers(): Promise<void>`
Delete all users except the current admin user.

### Plugin Management

#### `getPluginsMap(): Promise<Record<string, string>>`
Get a map of available plugins (slug -> file path).

#### `activatePlugin(slug: string): Promise<void>`
Activate a plugin by slug.

#### `deactivatePlugin(slug: string): Promise<void>`
Deactivate a plugin by slug.

### Theme Management

#### `activateTheme(slug: string): Promise<void>`
Activate a theme by slug.

#### `getCurrentThemeGlobalStylesPostId(): Promise<number>`
Get the current theme's global styles post ID.

#### `getThemeGlobalStylesRevisions(): Promise<any[]>`
Get revisions for the current theme's global styles.

### Template Management

#### `createTemplate(payload: CreateTemplatePayload): Promise<Template>`
Create a new template.

#### `deleteAllTemplates(): Promise<void>`
Delete all custom templates.

### Menu Management

#### `createNavigationMenu(payload: CreateNavigationMenuPayload): Promise<NavigationMenu>`
Create a navigation menu.

#### `createClassicMenu(payload: CreateClassicMenuPayload): Promise<ClassicMenu>`
Create a classic WordPress menu.

#### `getNavigationMenus(): Promise<NavigationMenu[]>`
Get all navigation menus.

#### `deleteAllMenus(): Promise<void>`
Delete all menus.

### Site Settings

#### `getSiteSettings(): Promise<SiteSettings>`
Get current site settings.

#### `updateSiteSettings(settings: Partial<SiteSettings>): Promise<SiteSettings>`
Update site settings.

### Preferences

#### `resetPreferences(): Promise<void>`
Reset user preferences to defaults.

### Blocks and Patterns

#### `createBlock(payload: CreateBlockPayload): Promise<Block>`
Create a reusable block.

#### `deleteAllBlocks(): Promise<void>`
Delete all reusable blocks.

#### `deleteAllPatternCategories(): Promise<void>`
Delete all pattern categories.

### Widgets

#### `addWidgetBlock(sidebarId: string, block: BlockRepresentation): Promise<void>`
Add a block to a widget area.

#### `deleteAllWidgets(): Promise<void>`
Delete all widgets.

### Gutenberg Experiments

#### `setGutenbergExperiments(experiments: string[]): Promise<void>`
Enable Gutenberg experimental features.

### REST API Utilities

#### `rest<T>(options: RestOptions): Promise<T>`
Make a direct REST API request.

**RestOptions:**
```typescript
interface RestOptions {
  path: string;
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  data?: any;
  params?: Record<string, any>;
}
```

#### `batchRest(requests: RestRequest[]): Promise<any[]>`
Make multiple REST API requests in a batch.

#### `getMaxBatchSize(): Promise<number>`
Get the maximum batch size supported by the server.

## Metrics Class Methods

### Web Vitals

#### `initWebVitals(reload?: boolean): Promise<void>`
Initialize web-vitals library for collecting performance metrics.

#### `getWebVitals(): Promise<WebVitalsMeasurements>`
Get web vitals measurements.

**WebVitalsMeasurements:**
```typescript
interface WebVitalsMeasurements {
  CLS?: number; // Cumulative Layout Shift
  FCP?: number; // First Contentful Paint
  FID?: number; // First Input Delay
  INP?: number; // Interaction to Next Paint
  LCP?: number; // Largest Contentful Paint
  TTFB?: number; // Time to First Byte
}
```

### Performance Metrics

#### `getTimeToFirstByte(): Promise<number>`
Get Time to First Byte using Navigation Timing API.

#### `getLargestContentfulPaint(): Promise<number>`
Get Largest Contentful Paint value.

#### `getCumulativeLayoutShift(): Promise<number>`
Get Cumulative Layout Shift value.

#### `getLoadingDurations(): Promise<LoadingDurations>`
Get comprehensive loading duration metrics.

#### `getServerTiming(fields?: string[]): Promise<Record<string, number>>`
Get server timing information from Server-Timing header.

### Chrome Tracing

#### `startTracing(options?: TracingOptions): Promise<void>`
Start Chrome tracing for performance analysis.

#### `stopTracing(): Promise<void>`
Stop tracing and save trace data.

#### `getEventDurations(eventType: EventType): number[]`
Get durations for specific event types.

**Event Types:** `'click' | 'focus' | 'focusin' | 'keydown' | 'keypress' | 'keyup' | 'mouseout' | 'mouseover'`

#### `getTypingEventDurations(): number[][]`
Get durations for typing events (keydown, keypress, keyup).

#### `getSelectionEventDurations(): number[][]`
Get durations for selection events (focus, focusin).

#### `getClickEventDurations(): number[][]`
Get durations for click events.

#### `getHoverEventDurations(): number[][]`
Get durations for hover events (mouseover, mouseout).

## Lighthouse Class Methods

#### `getReport(): Promise<LighthouseReport>`
Run Lighthouse audit and return performance metrics.

**LighthouseReport:**
```typescript
interface LighthouseReport {
  LCP: number; // Largest Contentful Paint
  TBT: number; // Total Blocking Time
  TTI: number; // Time to Interactive
  CLS: number; // Cumulative Layout Shift
  INP: number; // Interaction to Next Paint
}
```

## Test Fixtures

### Extended Test Function

The package provides an extended test function with built-in fixtures:

```typescript
test('Test name', async ({
  admin,        // Admin class instance
  editor,       // Editor class instance
  pageUtils,    // PageUtils class instance
  metrics,      // Metrics class instance
  lighthouse,   // Lighthouse class instance
  requestUtils, // RequestUtils class instance (worker-scoped)
  page,         // Enhanced Playwright Page with console logging
}) => {
  // Test implementation
});
```

### Fixture Scopes

- **Test-scoped**: `admin`, `editor`, `pageUtils`, `metrics`, `lighthouse`, `page`
- **Worker-scoped**: `requestUtils`, `lighthousePort`

Worker-scoped fixtures are shared across all tests in the same worker process, making them efficient for setup operations like authentication and data cleanup.

## Common Method Combinations

### Complete Post Creation Workflow
```javascript
// Create, edit, and publish a post
await admin.createNewPost({ title: 'My Post' });
await editor.insertBlock({ name: 'core/paragraph' });
await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Content');
await editor.publishPost();
```

### Block Editor Testing Pattern
```javascript
// Insert, select, and modify blocks
await editor.insertBlock({ name: 'core/heading' });
const blocks = await editor.getBlocks();
await editor.selectBlocks(blocks[0].clientId);
await editor.showBlockToolbar();
await editor.clickBlockToolbarButton('Bold');
```

### API-First Testing Pattern
```javascript
// Create content via API, then test UI
const post = await requestUtils.createPost({ title: 'API Post', status: 'draft' });
await admin.editPost(post.id);
await editor.publishPost();
```

### Performance Testing Pattern
```javascript
// Measure performance during interactions
await metrics.initWebVitals();
await admin.createNewPost();
const vitals = await metrics.getWebVitals();
expect(vitals.LCP).toBeLessThan(2500);
```

This method reference provides detailed information about all available methods, their parameters, return types, and usage examples for the WordPress E2E Test Utils Playwright package.
