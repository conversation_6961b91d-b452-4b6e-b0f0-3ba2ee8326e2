# WordPress E2E Test Utils Playwright - Troubleshooting Guide

## Common Issues and Solutions

### 1. Authentication Issues

#### Problem: Tests fail with authentication errors
```
Error: Request failed with status 401 - Unauthorized
```

**Solutions:**
- Verify WordPress credentials in your test setup
- Check if the WordPress site is accessible
- Ensure the admin user exists and has proper permissions

```javascript
// Correct authentication setup
const requestUtils = await RequestUtils.setup({
  user: { username: 'admin', password: 'password' },
  baseURL: 'http://localhost:8889'
});
```

#### Problem: Storage state not persisting
**Solutions:**
- Ensure the storage state directory exists
- Check file permissions for the storage state path
- Verify the path is correctly configured

```javascript
// Ensure directory exists
const STORAGE_STATE_PATH = path.join(process.cwd(), 'artifacts/storage-states/admin.json');
await fs.mkdir(path.dirname(STORAGE_STATE_PATH), { recursive: true });
```

### 2. Block Editor Issues

#### Problem: Canvas locator not working
```
Error: Locator not found: [name="editor-canvas"]
```

**Solutions:**
- Wait for the editor to fully load
- Check if you're in the correct editor context
- Verify the block editor is active (not classic editor)

```javascript
// Wait for editor to load
await page.waitForSelector('[name="editor-canvas"]');
await page.waitForFunction(() => window?.wp?.blocks && window?.wp?.data);

// Use canvas locator correctly
const paragraph = editor.canvas.locator('role=document[name="Paragraph block"i]');
```

#### Problem: Block insertion fails
```
Error: Block type 'core/paragraph' not found
```

**Solutions:**
- Ensure WordPress and Gutenberg are properly loaded
- Wait for block registration to complete
- Check if the block name is correct

```javascript
// Wait for blocks to be registered
await page.waitForFunction(() => {
  return window?.wp?.blocks?.getBlockType('core/paragraph');
});
```

### 3. Timing and Race Conditions

#### Problem: Elements not found or actions failing intermittently
**Solutions:**
- Use Playwright's built-in waiting mechanisms
- Add explicit waits for dynamic content
- Use `waitForLoadState` for page transitions

```javascript
// Good practices for waiting
await page.waitForLoadState('networkidle');
await expect(page.locator('.block-editor-writing-flow')).toBeVisible();
await page.waitForFunction(() => window.wp?.data?.select('core/editor'));
```

#### Problem: Tests are flaky
**Solutions:**
- Increase timeout values for slow operations
- Use more specific selectors
- Add retry logic for critical operations

```javascript
// Configure timeouts
test.setTimeout(60000); // 60 seconds

// Use specific selectors
await page.locator('[data-type="core/paragraph"]').first().click();

// Retry pattern
await expect(async () => {
  await editor.publishPost();
  await expect(page.locator('.notice-success')).toBeVisible();
}).toPass({ timeout: 30000 });
```

### 4. Performance Testing Issues

#### Problem: Web Vitals not collecting data
**Solutions:**
- Ensure the page has enough content to trigger metrics
- Wait for page interactions before collecting metrics
- Check if the web-vitals library loaded correctly

```javascript
// Proper web vitals setup
await metrics.initWebVitals();
await page.goto('/test-page/');
await page.waitForLoadState('networkidle');
await page.waitForTimeout(2000); // Allow metrics to collect
const vitals = await metrics.getWebVitals();
```

#### Problem: Lighthouse fails to run
**Solutions:**
- Ensure Chrome debugging port is available
- Check if the page is accessible
- Verify Lighthouse configuration

```javascript
// Check if lighthouse port is available
const lighthousePort = await getPort();
console.log('Using Lighthouse port:', lighthousePort);
```

### 5. REST API Issues

#### Problem: Batch requests failing
```
Error: Batch size exceeds maximum allowed
```

**Solutions:**
- Check the maximum batch size
- Split large batches into smaller chunks
- Use individual requests for complex operations

```javascript
// Check max batch size
const maxBatchSize = await requestUtils.getMaxBatchSize();
console.log('Max batch size:', maxBatchSize);

// Split requests if needed
const requests = [...]; // Your requests array
const chunks = [];
for (let i = 0; i < requests.length; i += maxBatchSize) {
  chunks.push(requests.slice(i, i + maxBatchSize));
}

for (const chunk of chunks) {
  await requestUtils.batchRest(chunk);
}
```

#### Problem: Media upload fails
**Solutions:**
- Check file permissions and paths
- Verify file size limits
- Ensure proper MIME type handling

```javascript
// Check if file exists before upload
const fs = require('fs');
const filePath = './test-assets/image.jpg';

if (!fs.existsSync(filePath)) {
  throw new Error(`File not found: ${filePath}`);
}

const stats = fs.statSync(filePath);
console.log('File size:', stats.size, 'bytes');
```

### 6. Environment Setup Issues

#### Problem: WordPress site not accessible
**Solutions:**
- Verify the WordPress installation is running
- Check the base URL configuration
- Ensure proper network connectivity

```javascript
// Test site accessibility
const response = await fetch('http://localhost:8889/wp-admin/');
if (!response.ok) {
  throw new Error(`WordPress site not accessible: ${response.status}`);
}
```

#### Problem: Plugin/theme not found
**Solutions:**
- Verify the plugin/theme is installed
- Check the correct slug/identifier
- Ensure proper permissions

```javascript
// Check available plugins
const plugins = await requestUtils.getPluginsMap();
console.log('Available plugins:', Object.keys(plugins));

// Check if specific plugin exists
if (!plugins['my-plugin/my-plugin.php']) {
  console.warn('Plugin not found: my-plugin');
}
```

## Debugging Techniques

### 1. Enable Verbose Logging
```javascript
// Add debug logging
test('Debug test', async ({ page, admin, editor }) => {
  page.on('console', msg => console.log('PAGE LOG:', msg.text()));
  page.on('pageerror', err => console.log('PAGE ERROR:', err.message));
  
  await admin.createNewPost();
  // ... rest of test
});
```

### 2. Take Screenshots on Failure
```javascript
// Playwright config
export default defineConfig({
  use: {
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
});

// Manual screenshot
test('Test with screenshot', async ({ page }) => {
  try {
    // Test actions
  } catch (error) {
    await page.screenshot({ path: 'debug-screenshot.png' });
    throw error;
  }
});
```

### 3. Inspect Element States
```javascript
// Check element visibility and state
const element = page.locator('.my-element');
console.log('Element visible:', await element.isVisible());
console.log('Element enabled:', await element.isEnabled());
console.log('Element text:', await element.textContent());
```

### 4. Network Monitoring
```javascript
// Monitor network requests
page.on('request', request => {
  console.log('REQUEST:', request.method(), request.url());
});

page.on('response', response => {
  console.log('RESPONSE:', response.status(), response.url());
});
```

## Best Practices for Reliable Tests

### 1. Test Isolation
```javascript
// Clean up before each test
test.beforeEach(async ({ requestUtils }) => {
  await Promise.all([
    requestUtils.deleteAllPosts(),
    requestUtils.deleteAllPages(),
    requestUtils.deleteAllComments(),
    requestUtils.resetPreferences()
  ]);
});
```

### 2. Proper Waiting Strategies
```javascript
// Wait for specific conditions
await page.waitForFunction(() => window.wp?.data?.select('core/editor'));
await expect(page.locator('.block-editor-writing-flow')).toBeVisible();
await page.waitForLoadState('networkidle');
```

### 3. Error Handling
```javascript
// Graceful error handling
test('Robust test', async ({ admin, editor }) => {
  try {
    await admin.createNewPost();
    await editor.insertBlock({ name: 'core/paragraph' });
  } catch (error) {
    console.error('Test failed:', error.message);
    // Take screenshot or perform cleanup
    throw error;
  }
});
```

### 4. Selective Test Execution
```javascript
// Skip tests based on conditions
test('Conditional test', async ({ requestUtils }) => {
  const plugins = await requestUtils.getPluginsMap();
  
  test.skip(!plugins['gutenberg/gutenberg.php'], 'Gutenberg plugin not available');
  
  // Test that requires Gutenberg
});
```

## Performance Optimization

### 1. Parallel Test Execution
```javascript
// Playwright config
export default defineConfig({
  workers: process.env.CI ? 2 : 4,
  fullyParallel: true,
});
```

### 2. Efficient Data Setup
```javascript
// Use API for data setup, UI for testing
test('Efficient test', async ({ requestUtils, admin, editor }) => {
  // Fast API setup
  const post = await requestUtils.createPost({
    title: 'Test Post',
    status: 'draft'
  });
  
  // UI testing
  await admin.editPost(post.id);
  await editor.publishPost();
});
```

### 3. Reuse Authentication
```javascript
// Worker-scoped authentication
test.describe('Post tests', () => {
  let requestUtils;
  
  test.beforeAll(async () => {
    requestUtils = await RequestUtils.setup({
      storageStatePath: './auth-state.json'
    });
  });
  
  // Tests use shared authentication
});
```

## Environment-Specific Issues

### 1. Docker/Container Issues
- Ensure proper network configuration
- Check port mappings
- Verify volume mounts for file uploads

### 2. CI/CD Pipeline Issues
- Use headless mode for CI environments
- Configure proper timeouts for slower CI machines
- Handle environment-specific URLs and credentials

```javascript
// CI-specific configuration
export default defineConfig({
  use: {
    headless: process.env.CI ? true : false,
    baseURL: process.env.CI ? 'http://wordpress:80' : 'http://localhost:8889',
  },
});
```

### 3. Local Development Issues
- Ensure WordPress is running locally
- Check for port conflicts
- Verify file permissions for uploads and storage

## Getting Help

### 1. Enable Debug Mode
```javascript
// Add to your test file
process.env.DEBUG = 'pw:api';
```

### 2. Check WordPress Error Logs
```bash
# Check WordPress error logs
tail -f /path/to/wordpress/wp-content/debug.log
```

### 3. Playwright Inspector
```bash
# Run tests with inspector
npx playwright test --debug
```

### 4. Common Log Locations
- WordPress: `wp-content/debug.log`
- Playwright: `test-results/`
- Browser console: Available through page events

This troubleshooting guide covers the most common issues encountered when using the WordPress E2E Test Utils Playwright package and provides practical solutions for each scenario.
