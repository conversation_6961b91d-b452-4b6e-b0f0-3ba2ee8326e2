# WordPress E2E Test Utils Playwright - Usage Guide

## Quick Start

### Installation and Setup

1. **Install the package:**
```bash
npm install @wordpress/e2e-test-utils-playwright --save-dev
```

2. **Basic test setup:**
```javascript
// tests/example.spec.js
import { test, expect } from '@wordpress/e2e-test-utils-playwright';

test('My first WordPress test', async ({ admin, editor }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Hello WordPress!');
  await editor.publishPost();
});
```

3. **Playwright configuration:**
```javascript
// playwright.config.js
import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  use: {
    baseURL: 'http://localhost:8889', // Your WordPress site URL
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
});
```

## Common Testing Patterns

### 1. Post and Page Management

#### Creating Posts
```javascript
test('Create different types of posts', async ({ admin, editor }) => {
  // Create a basic post
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('My post content');
  await editor.publishPost();

  // Create a page with custom settings
  await admin.createNewPost({
    postType: 'page',
    title: 'About Us',
    showWelcomeGuide: false,
    fullscreenMode: true
  });
});
```

#### Working with Block Content
```javascript
test('Add complex block structures', async ({ admin, editor }) => {
  await admin.createNewPost();
  
  // Insert a group block with nested content
  await editor.insertBlock({
    name: 'core/group',
    innerBlocks: [
      {
        name: 'core/heading',
        attributes: { content: 'Main Heading', level: 2 }
      },
      {
        name: 'core/paragraph',
        attributes: { content: 'This is a paragraph inside a group.' }
      }
    ]
  });

  // Insert an image block
  await editor.insertBlock({
    name: 'core/image',
    attributes: {
      url: 'https://example.com/image.jpg',
      alt: 'Example image'
    }
  });
});
```

### 2. Editor Interactions

#### Block Selection and Manipulation
```javascript
test('Select and modify blocks', async ({ admin, editor }) => {
  await admin.createNewPost();
  
  // Insert multiple blocks
  await editor.insertBlock({ name: 'core/paragraph' });
  await editor.insertBlock({ name: 'core/heading' });
  
  // Get all blocks
  const blocks = await editor.getBlocks();
  expect(blocks).toHaveLength(2);
  
  // Select the first block
  await editor.selectBlocks(blocks[0].clientId);
  
  // Transform block type
  await editor.transformBlockTo('core/quote');
  
  // Use block toolbar
  await editor.showBlockToolbar();
  await editor.clickBlockToolbarButton('Bold');
});
```

#### Canvas Interactions
```javascript
test('Interact with editor canvas', async ({ admin, editor }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  
  // Type in the canvas
  const paragraph = editor.canvas.locator('role=document[name="Paragraph block"i]');
  await paragraph.fill('Initial text');
  
  // Select text and format
  await paragraph.selectText();
  await editor.clickBlockToolbarButton('Bold');
  
  // Add more content
  await paragraph.press('End');
  await paragraph.type(' Additional text.');
});
```

### 3. API-Based Testing

#### Content Management via REST API
```javascript
test('Manage content via API', async ({ requestUtils }) => {
  // Clean slate
  await requestUtils.deleteAllPosts();
  await requestUtils.deleteAllPages();
  
  // Create test content
  const post = await requestUtils.createPost({
    title: 'API Created Post',
    content: '<!-- wp:paragraph --><p>Created via REST API</p><!-- /wp:paragraph -->',
    status: 'publish'
  });
  
  const page = await requestUtils.createPage({
    title: 'API Created Page',
    content: '<!-- wp:heading --><h2>Page Heading</h2><!-- /wp:heading -->',
    status: 'publish'
  });
  
  expect(post.id).toBeDefined();
  expect(page.id).toBeDefined();
});
```

#### User and Permission Testing
```javascript
test('Test user permissions', async ({ requestUtils }) => {
  // Create a test user
  const user = await requestUtils.createUser({
    username: 'testauthor',
    email: '<EMAIL>',
    password: 'testpass123',
    roles: ['author']
  });
  
  // Create RequestUtils for the new user
  const authorRequestUtils = await RequestUtils.setup({
    user: { username: 'testauthor', password: 'testpass123' }
  });
  
  // Test what the author can do
  const authorPost = await authorRequestUtils.createPost({
    title: 'Author Post',
    status: 'draft' // Authors might not be able to publish directly
  });
  
  expect(authorPost.status).toBe('draft');
});
```

### 4. Performance Testing

#### Web Vitals Monitoring
```javascript
test('Monitor Core Web Vitals', async ({ page, metrics }) => {
  // Initialize web vitals tracking
  await metrics.initWebVitals();
  
  // Navigate to a page
  await page.goto('/sample-page/');
  
  // Get performance metrics
  const vitals = await metrics.getWebVitals();
  
  // Assert performance benchmarks
  expect(vitals.LCP).toBeLessThan(2500); // LCP under 2.5s
  expect(vitals.FID).toBeLessThan(100);  // FID under 100ms
  expect(vitals.CLS).toBeLessThan(0.1);  // CLS under 0.1
  
  console.log('Performance Metrics:', vitals);
});
```

#### Lighthouse Auditing
```javascript
test('Run Lighthouse audit', async ({ page, lighthouse }) => {
  await page.goto('/');
  
  const report = await lighthouse.getReport();
  
  // Check Lighthouse scores
  expect(report.LCP).toBeLessThan(2500);
  expect(report.TTI).toBeLessThan(3800);
  expect(report.CLS).toBeLessThan(0.1);
  
  console.log('Lighthouse Report:', report);
});
```

#### Interaction Performance
```javascript
test('Measure typing performance', async ({ admin, editor, metrics }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  
  // Start tracing
  await metrics.startTracing();
  
  // Perform typing actions
  const paragraph = editor.canvas.locator('role=document[name="Paragraph block"i]');
  await paragraph.type('This is a performance test for typing speed.');
  
  // Stop tracing and analyze
  await metrics.stopTracing();
  const typingDurations = metrics.getTypingEventDurations();
  
  // Assert performance expectations
  const avgTypingTime = typingDurations[0].reduce((a, b) => a + b, 0) / typingDurations[0].length;
  expect(avgTypingTime).toBeLessThan(50); // Average keydown should be under 50ms
});
```

### 5. Site Configuration Testing

#### Plugin Management
```javascript
test('Manage plugins', async ({ requestUtils }) => {
  // Get available plugins
  const plugins = await requestUtils.getPluginsMap();
  console.log('Available plugins:', Object.keys(plugins));
  
  // Activate a plugin
  if (plugins['gutenberg/gutenberg.php']) {
    await requestUtils.activatePlugin('gutenberg');
  }
  
  // Deactivate a plugin
  await requestUtils.deactivatePlugin('hello-dolly');
});
```

#### Theme and Customization
```javascript
test('Theme and customization', async ({ requestUtils }) => {
  // Activate a theme
  await requestUtils.activateTheme('twentytwentyfour');
  
  // Get global styles
  const globalStylesId = await requestUtils.getCurrentThemeGlobalStylesPostId();
  expect(globalStylesId).toBeDefined();
  
  // Update site settings
  await requestUtils.updateSiteSettings({
    title: 'Test Site Title',
    description: 'A site for testing',
    start_of_week: 1 // Monday
  });
});
```

### 6. Media and File Handling

#### File Upload Testing
```javascript
test('Upload and manage media', async ({ requestUtils, pageUtils }) => {
  // Upload via API
  const media = await requestUtils.uploadMedia('./test-files/sample-image.jpg');
  expect(media.id).toBeDefined();
  expect(media.mime_type).toBe('image/jpeg');
  
  // Test drag and drop upload
  await admin.visitAdminPage('upload.php');
  await pageUtils.dragFiles('#drag-drop-area', ['./test-files/sample-document.pdf']);
  
  // Verify upload
  await expect(page.locator('.media-item')).toBeVisible();
});
```

### 7. Navigation and Menu Testing

#### Menu Creation and Management
```javascript
test('Create and manage menus', async ({ requestUtils, admin }) => {
  // Create a navigation menu via API
  const menu = await requestUtils.createNavigationMenu({
    title: 'Test Navigation',
    content: `
      <!-- wp:navigation-link {"label":"Home","url":"/"} /-->
      <!-- wp:navigation-link {"label":"About","url":"/about"} /-->
    `
  });
  
  // Create a classic menu
  const classicMenu = await requestUtils.createClassicMenu({
    name: 'Header Menu',
    locations: ['primary']
  });
  
  expect(menu.id).toBeDefined();
  expect(classicMenu.term_id).toBeDefined();
});
```

### 8. Advanced Testing Patterns

#### Custom Block Testing
```javascript
test('Test custom blocks', async ({ admin, editor }) => {
  await admin.createNewPost();
  
  // Insert a custom block (assuming it's registered)
  await editor.insertBlock({
    name: 'my-plugin/custom-block',
    attributes: {
      customAttribute: 'test value',
      showTitle: true
    }
  });
  
  // Interact with custom block controls
  const customBlock = editor.canvas.locator('[data-type="my-plugin/custom-block"]');
  await expect(customBlock).toBeVisible();
  
  // Test block settings
  await editor.openDocumentSettingsSidebar();
  await page.locator('.block-editor-block-inspector').getByLabel('Custom Setting').check();
});
```

#### Multi-site Testing
```javascript
test('Multi-site functionality', async ({ requestUtils }) => {
  // Switch to different site in network
  const site2RequestUtils = await RequestUtils.setup({
    baseURL: 'http://site2.localhost:8889',
    user: { username: 'admin', password: 'password' }
  });
  
  // Create content on different sites
  await requestUtils.createPost({ title: 'Main Site Post' });
  await site2RequestUtils.createPost({ title: 'Site 2 Post' });
});
```

#### Accessibility Testing
```javascript
test('Accessibility compliance', async ({ page, admin, editor }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/image' });
  
  // Test that alt text is required
  const imageBlock = editor.canvas.locator('[data-type="core/image"]');
  await imageBlock.locator('button').click(); // Open media library
  
  // Select an image without alt text
  await page.locator('.media-modal .attachment').first().click();
  
  // Verify alt text field is present and required
  const altField = page.locator('.media-modal input[aria-label*="Alt"]');
  await expect(altField).toBeVisible();
  
  await altField.fill('Descriptive alt text');
  await page.locator('.media-modal .media-button-select').click();
});
```

## Testing Best Practices

### 1. Test Organization
```javascript
// Group related tests
test.describe('Post Editor', () => {
  test.beforeEach(async ({ requestUtils }) => {
    await requestUtils.deleteAllPosts();
  });

  test('should create post', async ({ admin, editor }) => {
    // Test implementation
  });

  test('should edit post', async ({ admin, editor }) => {
    // Test implementation
  });
});
```

### 2. Data Management
```javascript
// Clean up before each test
test.beforeEach(async ({ requestUtils }) => {
  await Promise.all([
    requestUtils.deleteAllPosts(),
    requestUtils.deleteAllPages(),
    requestUtils.deleteAllComments(),
    requestUtils.resetPreferences()
  ]);
});
```

### 3. Waiting Strategies
```javascript
// Wait for specific conditions
await page.waitForLoadState('networkidle');
await page.waitForSelector('.block-editor-writing-flow');
await page.waitForFunction(() => window.wp?.data?.select('core/editor'));

// Use Playwright's auto-waiting
await expect(page.locator('.notice-success')).toBeVisible();
```

### 4. Error Handling
```javascript
test('should handle errors gracefully', async ({ admin, editor }) => {
  try {
    await admin.createNewPost();
    await editor.insertBlock({ name: 'invalid/block' });
  } catch (error) {
    // Handle expected errors
    expect(error.message).toContain('Block type not found');
  }
});
```

### 5. Cross-browser Testing
```javascript
// playwright.config.js
export default defineConfig({
  projects: [
    { name: 'chromium', use: { ...devices['Desktop Chrome'] } },
    { name: 'firefox', use: { ...devices['Desktop Firefox'] } },
    { name: 'webkit', use: { ...devices['Desktop Safari'] } },
  ],
});
```

This usage guide provides practical examples and patterns for effectively using the WordPress E2E Test Utils Playwright package in various testing scenarios.
