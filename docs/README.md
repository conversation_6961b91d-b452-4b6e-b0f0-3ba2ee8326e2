# WordPress E2E Test Utils Playwright - Complete Documentation

## Overview

This documentation provides comprehensive guidance for using the `@wordpress/e2e-test-utils-playwright` package, a powerful end-to-end testing framework specifically designed for WordPress and Gutenberg development.

## Package Information

- **Package**: `@wordpress/e2e-test-utils-playwright`
- **Version**: 1.30.0
- **License**: GPL-2.0-or-later
- **Repository**: [WordPress/gutenberg](https://github.com/WordPress/gutenberg/tree/HEAD/packages/e2e-test-utils-playwright)

## What's Included

This package provides:

- **Admin utilities** - WordPress admin interface interactions
- **Editor utilities** - Gutenberg block editor testing tools
- **Page utilities** - Generic web page interaction helpers
- **Request utilities** - WordPress REST API testing tools
- **Performance utilities** - Web vitals and Lighthouse integration
- **Test fixtures** - Pre-configured Playwright test extensions

## Quick Start

### Installation
```bash
npm install @wordpress/e2e-test-utils-playwright --save-dev
```

### Basic Usage
```javascript
import { test, expect } from '@wordpress/e2e-test-utils-playwright';

test('Create a post', async ({ admin, editor }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Hello World!');
  await editor.publishPost();
});
```

## Documentation Structure

### 📚 [API Reference](./API_REFERENCE.md)
Complete reference documentation for all classes, methods, and interfaces. Essential for understanding what's available and how to use each component.

**Key Sections:**
- Admin Class - WordPress admin interface utilities
- Editor Class - Block editor interactions and canvas access
- PageUtils Class - Generic page interaction helpers
- RequestUtils Class - REST API utilities for content management
- Metrics Class - Performance testing with web vitals
- Lighthouse Class - Performance auditing integration
- Test Fixtures - Extended Playwright test configuration

### 🚀 [Usage Guide](./USAGE_GUIDE.md)
Practical patterns and common testing scenarios with detailed explanations.

**Key Topics:**
- Post and page management workflows
- Block editor interactions and canvas manipulation
- API-based testing for efficient data setup
- Performance monitoring and optimization
- Site configuration and plugin management
- Best practices for reliable test suites

### 🔧 [Method Reference](./METHOD_REFERENCE.md)
Detailed method signatures, parameters, and return types for all utility classes.

**Organized by Class:**
- Admin methods for navigation and post creation
- Editor methods for block manipulation and publishing
- PageUtils methods for keyboard, clipboard, and file operations
- RequestUtils methods for REST API interactions
- Metrics methods for performance measurement
- Common method combinations and patterns

### 💡 [Examples](./EXAMPLES.md)
Real-world testing scenarios from basic to advanced use cases.

**Example Categories:**
- Basic Examples - Simple post/page creation
- Intermediate Examples - Complex workflows and interactions
- Advanced Examples - Performance testing, accessibility, multi-site
- Complete Workflows - End-to-end testing scenarios

### 🔍 [Troubleshooting](./TROUBLESHOOTING.md)
Common issues, debugging techniques, and solutions.

**Troubleshooting Areas:**
- Authentication and setup issues
- Block editor and canvas problems
- Timing and race conditions
- Performance testing challenges
- Environment-specific issues
- Debugging techniques and best practices

## Core Concepts

### Test Fixtures
The package extends Playwright's test with WordPress-specific fixtures:

```javascript
test('Example', async ({
  admin,        // WordPress admin utilities
  editor,       // Block editor utilities
  pageUtils,    // Generic page utilities
  requestUtils, // REST API utilities
  metrics,      // Performance measurement
  lighthouse,   // Lighthouse auditing
  page          // Enhanced Playwright page
}) => {
  // Your test code here
});
```

### Canvas Interaction
The editor provides a special `canvas` property for interacting with the block editor iframe:

```javascript
// Interact with blocks in the editor canvas
await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Content');
```

### API-First Testing
Use REST API utilities for efficient test data setup:

```javascript
// Fast API setup
const post = await requestUtils.createPost({ title: 'Test Post', status: 'draft' });

// UI testing
await admin.editPost(post.id);
await editor.publishPost();
```

### Performance Integration
Built-in performance monitoring with web vitals and Lighthouse:

```javascript
await metrics.initWebVitals();
const vitals = await metrics.getWebVitals();
expect(vitals.LCP).toBeLessThan(2500);
```

## Testing Patterns

### 1. Content Creation Pattern
```javascript
test('Content creation', async ({ admin, editor }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Content');
  await editor.publishPost();
});
```

### 2. API Setup Pattern
```javascript
test('API setup', async ({ requestUtils, admin, editor }) => {
  // Setup via API
  const post = await requestUtils.createPost({ title: 'Test', status: 'draft' });
  
  // Test via UI
  await admin.editPost(post.id);
  await editor.publishPost();
});
```

### 3. Performance Testing Pattern
```javascript
test('Performance', async ({ page, metrics }) => {
  await metrics.initWebVitals();
  await page.goto('/test-page/');
  const vitals = await metrics.getWebVitals();
  expect(vitals.LCP).toBeLessThan(2500);
});
```

## Configuration

### Playwright Configuration
```javascript
// playwright.config.js
import { defineConfig } from '@playwright/test';

export default defineConfig({
  testDir: './tests',
  use: {
    baseURL: 'http://localhost:8889',
    screenshot: 'only-on-failure',
    video: 'retain-on-failure',
  },
  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
  ],
});
```

### Environment Variables
- `STORAGE_STATE_PATH` - Authentication storage path
- `WP_BASE_URL` - WordPress site URL
- `DEBUG` - Enable debug logging

## Best Practices

### 1. Test Organization
```javascript
test.describe('Feature tests', () => {
  test.beforeEach(async ({ requestUtils }) => {
    await requestUtils.deleteAllPosts();
  });
  
  // Related tests
});
```

### 2. Efficient Data Management
```javascript
// Use API for setup, UI for testing
const post = await requestUtils.createPost({ title: 'Test' });
await admin.editPost(post.id);
```

### 3. Proper Waiting
```javascript
await page.waitForLoadState('networkidle');
await expect(element).toBeVisible();
```

### 4. Error Handling
```javascript
try {
  await admin.createNewPost();
} catch (error) {
  await page.screenshot({ path: 'error.png' });
  throw error;
}
```

## Common Use Cases

### WordPress Plugin Testing
- Test plugin activation/deactivation
- Verify custom block functionality
- Test plugin settings and configuration
- Validate plugin compatibility

### Theme Development
- Test theme activation and customization
- Verify responsive design
- Test theme-specific features
- Validate accessibility compliance

### Content Management
- Test post/page creation workflows
- Verify media upload and management
- Test comment moderation
- Validate user permissions

### Performance Optimization
- Monitor Core Web Vitals
- Run Lighthouse audits
- Measure interaction performance
- Test under various network conditions

### Site Administration
- Test user management
- Verify site settings configuration
- Test backup and restore procedures
- Validate security features

## Integration Examples

### CI/CD Pipeline
```yaml
# GitHub Actions example
- name: Run E2E Tests
  run: |
    npm install
    npx playwright install
    npm run test:e2e
```

### Docker Integration
```dockerfile
# WordPress testing environment
FROM wordpress:latest
COPY ./test-plugins /var/www/html/wp-content/plugins/
COPY ./test-themes /var/www/html/wp-content/themes/
```

### Local Development
```bash
# Start WordPress locally
docker-compose up -d

# Run tests
npm run test:e2e

# Run specific test
npx playwright test --grep "post creation"
```

## Support and Resources

### Getting Help
1. Check the [Troubleshooting Guide](./TROUBLESHOOTING.md)
2. Review [Examples](./EXAMPLES.md) for similar use cases
3. Consult the [API Reference](./API_REFERENCE.md) for method details
4. Search [WordPress Gutenberg issues](https://github.com/WordPress/gutenberg/issues)

### Contributing
This package is part of the WordPress Gutenberg project. See the [main contributor guide](https://github.com/WordPress/gutenberg/tree/HEAD/CONTRIBUTING.md) for information on contributing.

### Related Resources
- [Playwright Documentation](https://playwright.dev/)
- [WordPress REST API](https://developer.wordpress.org/rest-api/)
- [Gutenberg Handbook](https://developer.wordpress.org/block-editor/)
- [WordPress Coding Standards](https://developer.wordpress.org/coding-standards/)

## Version Compatibility

- **Node.js**: >=18.12.0
- **NPM**: >=8.19.2
- **WordPress**: >=5.6.0
- **Gutenberg**: >=9.2.0
- **Playwright**: >=1.0.0

## License

GPL-2.0-or-later - See the [WordPress Gutenberg repository](https://github.com/WordPress/gutenberg) for full license details.

---

This documentation provides everything needed to effectively use the WordPress E2E Test Utils Playwright package for comprehensive WordPress testing. Start with the [Usage Guide](./USAGE_GUIDE.md) for practical examples, then refer to the [API Reference](./API_REFERENCE.md) for detailed method information.
