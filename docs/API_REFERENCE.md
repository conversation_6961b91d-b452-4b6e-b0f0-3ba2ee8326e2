# WordPress E2E Test Utils Playwright - API Reference

## Overview

The `@wordpress/e2e-test-utils-playwright` package provides comprehensive end-to-end testing utilities for WordPress and Gutenberg using <PERSON>wright. This package is designed for testing WordPress admin interfaces, block editors, and REST API interactions.

## Package Information

- **Name**: `@wordpress/e2e-test-utils-playwright`
- **Version**: 1.30.0
- **License**: GPL-2.0-or-later
- **Node.js**: >=18.12.0
- **NPM**: >=8.19.2
- **Peer Dependencies**: `@playwright/test` >=1

## Installation

```bash
npm install @wordpress/e2e-test-utils-playwright --save-dev
```

## Core Exports

The package exports the following main classes and utilities:

```typescript
export { Admin } from './admin';
export { Editor } from './editor';
export { PageUtils } from './page-utils';
export { RequestUtils } from './request-utils';
export { Metrics } from './metrics';
export { Lighthouse } from './lighthouse';
export { test, expect } from './test';
```

## Test Fixtures

The package extends <PERSON><PERSON>'s test with custom fixtures:

```typescript
const test = base.extend<{
  admin: Admin;
  editor: Editor;
  pageUtils: PageUtils;
  snapshotConfig: void;
  metrics: Metrics;
  lighthouse: Lighthouse;
}, {
  requestUtils: RequestUtils;
  lighthousePort: number;
}>
```

### Usage Example

```javascript
import { test, expect } from '@wordpress/e2e-test-utils-playwright';

test('Create a new post', async ({ admin, editor }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Hello World');
  await editor.publishPost();
});
```

## Admin Class

The `Admin` class provides utilities for WordPress admin interface interactions.

### Constructor

```typescript
constructor({ page, pageUtils, editor }: {
  page: Page;
  pageUtils: PageUtils;
  editor: Editor;
})
```

### Methods

#### `createNewPost(options?: NewPostOptions)`

Creates a new post with optional configuration.

**Parameters:**
- `options.postType?: string` - Post type (default: 'post')
- `options.title?: string` - Post title
- `options.content?: string` - Post content
- `options.excerpt?: string` - Post excerpt
- `options.showWelcomeGuide?: boolean` - Show welcome guide (default: false)
- `options.fullscreenMode?: boolean` - Enable fullscreen mode (default: false)

**Example:**
```javascript
await admin.createNewPost({
  postType: 'page',
  title: 'My New Page',
  content: 'Page content here',
  showWelcomeGuide: false
});
```

#### `editPost(postId: number)`

Navigate to edit an existing post.

#### `visitAdminPage(adminPath: string, query?: string)`

Navigate to any WordPress admin page.

**Example:**
```javascript
await admin.visitAdminPage('options-general.php');
await admin.visitAdminPage('edit.php', 'post_type=page');
```

#### `visitSiteEditor(query?: URLSearchParams)`

Navigate to the Site Editor.

#### `getPageError()`

Get any page errors that occurred during navigation.

## Editor Class

The `Editor` class provides utilities for WordPress Block Editor interactions.

### Constructor

```typescript
constructor({ page }: { page: Page })
```

### Properties

#### `canvas: FrameLocator`

Access to the editor canvas iframe for interacting with blocks.

**Example:**
```javascript
await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Text content');
```

### Methods

#### `insertBlock(blockRepresentation: BlockRepresentation, options?)`

Insert a block into the editor.

**BlockRepresentation Interface:**
```typescript
interface BlockRepresentation {
  name: string;
  attributes?: Object;
  innerBlocks?: BlockRepresentation[];
}
```

**Example:**
```javascript
await editor.insertBlock({
  name: 'core/paragraph',
  attributes: { content: 'Hello World' }
});

// Insert block with inner blocks
await editor.insertBlock({
  name: 'core/group',
  innerBlocks: [
    { name: 'core/paragraph', attributes: { content: 'Nested paragraph' } }
  ]
});
```

#### `setContent(content: string)`

Set the entire post content.

#### `getEditedPostContent()`

Get the current post content as HTML.

#### `getBlocks()`

Get all blocks in the editor as an array.

#### `selectBlocks(startClientId: string, endClientId?: string)`

Select one or more blocks.

#### `publishPost()`

Publish the current post.

#### `saveDraft()`

Save the current post as a draft.

#### `openPreviewPage()`

Open the post preview in a new page.

#### `clickBlockToolbarButton(label: string)`

Click a button in the block toolbar.

#### `clickBlockOptionsMenuItem(label: string)`

Click an item in the block options menu.

#### `showBlockToolbar()`

Show the block toolbar.

#### `setIsFixedToolbar(isFixed: boolean)`

Set whether the toolbar is fixed.

#### `openDocumentSettingsSidebar()`

Open the document settings sidebar.

#### `setPreferences(scope: string, preferences: Object)`

Set editor preferences.

**Example:**
```javascript
await editor.setPreferences('core/edit-post', {
  welcomeGuide: false,
  fullscreenMode: true
});
```

#### `transformBlockTo(name: string)`

Transform the selected block to another block type.

#### `switchEditorTool(tool: string)`

Switch to a different editor tool (e.g., 'edit', 'select').

#### `switchToLegacyCanvas()`

Switch to legacy canvas mode.

#### `saveSiteEditorEntities()`

Save entities in the Site Editor.

## PageUtils Class

Generic Playwright utilities for web page interactions.

### Constructor

```typescript
constructor({ page, browserName }: {
  page: Page;
  browserName: PlaywrightWorkerOptions['browserName'];
})
```

### Methods

#### `pressKeys(key: string, options?: Options)`

Press keyboard keys with support for modifier combinations.

**Supported Modifiers:**
- `primary` - Cmd on Mac, Ctrl on others
- `primaryShift` - Cmd+Shift on Mac, Ctrl+Shift on others
- `primaryAlt` - Cmd+Alt on Mac, Ctrl+Alt on others
- `secondary` - Shift+Alt+Cmd on Mac, Shift+Alt+Ctrl on others
- `access` - Ctrl+Alt on Mac, Alt on others
- `ctrl`, `alt`, `shift` - Direct modifiers

**Examples:**
```javascript
await pageUtils.pressKeys('primary+a'); // Select all
await pageUtils.pressKeys('primary+c'); // Copy
await pageUtils.pressKeys('primary+v'); // Paste
await pageUtils.pressKeys('Escape');
await pageUtils.pressKeys('Enter', { times: 3 }); // Press Enter 3 times
```

#### `setClipboardData({ plainText, html })`

Set clipboard data for paste operations.

**Example:**
```javascript
await pageUtils.setClipboardData({
  plainText: 'Hello World',
  html: '<p>Hello World</p>'
});
await pageUtils.pressKeys('primary+v'); // Paste the data
```

#### `dragFiles(selector: string, files: string[])`

Drag and drop files onto an element.

#### `isCurrentURL(expectedURL: string)`

Check if the current URL matches the expected URL.

#### `setBrowserViewport(viewport: { width: number; height: number })`

Set the browser viewport size.

#### `emulateNetworkConditions(conditions: NetworkConditions)`

Emulate network conditions for testing.

## RequestUtils Class

Utilities for interacting with WordPress REST API.

### Setup

```typescript
const requestUtils = await RequestUtils.setup({
  user: { username: 'admin', password: 'password' },
  baseURL: 'http://localhost:8889',
  storageStatePath: './storage-state.json'
});
```

### Authentication

#### `login()`

Authenticate with WordPress and store session.

#### `setupRest()`

Setup REST API authentication.

### Content Management

#### Posts

```javascript
// Create a post
const post = await requestUtils.createPost({
  title: 'Test Post',
  content: 'Post content',
  status: 'publish'
});

// Delete all posts
await requestUtils.deleteAllPosts();
```

#### Pages

```javascript
// Create a page
const page = await requestUtils.createPage({
  title: 'Test Page',
  content: 'Page content',
  status: 'publish'
});

// Delete all pages
await requestUtils.deleteAllPages();
```

#### Media

```javascript
// Upload media
const media = await requestUtils.uploadMedia('./image.jpg');

// List media
const mediaList = await requestUtils.listMedia();

// Delete media
await requestUtils.deleteMedia(mediaId);

// Delete all media
await requestUtils.deleteAllMedia();
```

#### Comments

```javascript
// Create comment
const comment = await requestUtils.createComment({
  post: postId,
  content: 'Comment content'
});

// Delete all comments
await requestUtils.deleteAllComments();
```

### Site Management

#### Users

```javascript
// Create user
const user = await requestUtils.createUser({
  username: 'testuser',
  email: '<EMAIL>',
  password: 'password'
});

// Delete all users (except admin)
await requestUtils.deleteAllUsers();
```

#### Plugins

```javascript
// Get plugins map
const plugins = await requestUtils.getPluginsMap();

// Activate plugin
await requestUtils.activatePlugin('plugin-slug');

// Deactivate plugin
await requestUtils.deactivatePlugin('plugin-slug');
```

#### Themes

```javascript
// Activate theme
await requestUtils.activateTheme('theme-slug');

// Get theme global styles
const stylesId = await requestUtils.getCurrentThemeGlobalStylesPostId();
```

#### Templates

```javascript
// Create template
const template = await requestUtils.createTemplate({
  slug: 'custom-template',
  content: 'Template content'
});

// Delete all templates
await requestUtils.deleteAllTemplates();
```

#### Menus

```javascript
// Create navigation menu
const menu = await requestUtils.createNavigationMenu({
  title: 'Main Menu',
  content: 'Menu content'
});

// Create classic menu
const classicMenu = await requestUtils.createClassicMenu({
  name: 'Header Menu'
});

// Get navigation menus
const menus = await requestUtils.getNavigationMenus();

// Delete all menus
await requestUtils.deleteAllMenus();
```

### Settings and Preferences

#### Site Settings

```javascript
// Get site settings
const settings = await requestUtils.getSiteSettings();

// Update site settings
await requestUtils.updateSiteSettings({
  title: 'New Site Title',
  description: 'Site description'
});
```

#### User Preferences

```javascript
// Reset preferences
await requestUtils.resetPreferences();
```

#### Gutenberg Experiments

```javascript
// Enable Gutenberg experiments
await requestUtils.setGutenbergExperiments(['experiment-name']);
```

### Widgets

```javascript
// Add widget block
await requestUtils.addWidgetBlock('sidebar-1', {
  name: 'core/paragraph',
  attributes: { content: 'Widget content' }
});

// Delete all widgets
await requestUtils.deleteAllWidgets();
```

### Blocks and Patterns

```javascript
// Create reusable block
const block = await requestUtils.createBlock({
  title: 'My Block',
  content: 'Block content'
});

// Delete all blocks
await requestUtils.deleteAllBlocks();

// Delete all pattern categories
await requestUtils.deleteAllPatternCategories();
```

### REST API Utilities

#### `rest<T>(options: RestOptions): Promise<T>`

Make direct REST API calls.

**Example:**
```javascript
const posts = await requestUtils.rest({
  path: '/wp/v2/posts',
  method: 'GET',
  params: { per_page: 10 }
});
```

#### `batchRest(requests: RestRequest[])`

Make batch REST API requests.

**Example:**
```javascript
await requestUtils.batchRest([
  { path: '/wp/v2/posts', method: 'POST', data: { title: 'Post 1' } },
  { path: '/wp/v2/posts', method: 'POST', data: { title: 'Post 2' } }
]);
```

## Metrics Class

Performance testing utilities using web-vitals and Chrome tracing.

### Constructor

```typescript
constructor({ page }: { page: Page })
```

### Web Vitals

#### `initWebVitals(reload?: boolean)`

Initialize web-vitals library for collecting performance metrics.

#### `getWebVitals(): Promise<WebVitalsMeasurements>`

Get web vitals measurements (CLS, FCP, FID, INP, LCP, TTFB).

**Example:**
```javascript
await metrics.initWebVitals();
const vitals = await metrics.getWebVitals();
console.log('LCP:', vitals.LCP);
console.log('CLS:', vitals.CLS);
```

### Performance Metrics

#### `getTimeToFirstByte(): Promise<number>`

Get Time to First Byte (TTFB) using Navigation Timing API.

#### `getLargestContentfulPaint(): Promise<number>`

Get Largest Contentful Paint (LCP) value.

#### `getCumulativeLayoutShift(): Promise<number>`

Get Cumulative Layout Shift (CLS) value.

#### `getLoadingDurations()`

Get comprehensive loading duration metrics.

#### `getServerTiming(fields?: string[])`

Get server timing information from Server-Timing header.

### Chrome Tracing

#### `startTracing(options?)`

Start Chrome tracing for performance analysis.

#### `stopTracing()`

Stop tracing and save trace data.

#### Event Duration Methods

```javascript
// Get typing event durations
const typingDurations = metrics.getTypingEventDurations();

// Get selection event durations
const selectionDurations = metrics.getSelectionEventDurations();

// Get click event durations
const clickDurations = metrics.getClickEventDurations();

// Get hover event durations
const hoverDurations = metrics.getHoverEventDurations();

// Get specific event durations
const keydownDurations = metrics.getEventDurations('keydown');
```

## Lighthouse Class

Lighthouse performance auditing integration.

### Constructor

```typescript
constructor({ page, port }: { page: Page; port: number })
```

### Methods

#### `getReport(): Promise<Record<string, number>>`

Run Lighthouse audit and return performance metrics.

**Returns metrics for:**
- LCP (Largest Contentful Paint)
- TBT (Total Blocking Time)
- TTI (Time to Interactive)
- CLS (Cumulative Layout Shift)
- INP (Interaction to Next Paint)

**Example:**
```javascript
const report = await lighthouse.getReport();
console.log('Lighthouse LCP:', report.LCP);
console.log('Lighthouse TTI:', report.TTI);
```

## Configuration

### Environment Variables

- `STORAGE_STATE_PATH` - Path to store authentication state (default: `artifacts/storage-states/admin.json`)

### Default Configuration

The package includes default configuration in `src/config.ts`:

```typescript
export const WP_ADMIN_USER = {
  username: 'admin',
  password: 'password'
};

export const WP_BASE_URL = 'http://localhost:8889';
```

## Error Handling

The package includes comprehensive console error filtering to ignore common WordPress warnings and focus on actual test failures. Filtered messages include:

- jQuery migrate warnings
- SameSite cookie warnings
- Non-unique ID warnings
- Layout forced warnings
- Third-party library deprecation warnings

## Best Practices

### Test Structure

```javascript
import { test, expect } from '@wordpress/e2e-test-utils-playwright';

test.describe('Post Creation', () => {
  test.beforeEach(async ({ requestUtils }) => {
    // Clean up before each test
    await requestUtils.deleteAllPosts();
  });

  test('should create and publish a post', async ({ admin, editor }) => {
    await admin.createNewPost();
    await editor.insertBlock({ name: 'core/paragraph' });
    await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Test content');
    await editor.publishPost();
    
    // Verify post was published
    expect(await editor.page.locator('.notice-success')).toBeVisible();
  });
});
```

### Performance Testing

```javascript
test('should meet performance benchmarks', async ({ page, metrics }) => {
  await metrics.initWebVitals();
  await page.goto('/');
  
  const vitals = await metrics.getWebVitals();
  expect(vitals.LCP).toBeLessThan(2500); // LCP should be under 2.5s
  expect(vitals.CLS).toBeLessThan(0.1);  // CLS should be under 0.1
});
```

### API Testing

```javascript
test('should manage content via API', async ({ requestUtils }) => {
  // Create test data
  const post = await requestUtils.createPost({
    title: 'API Test Post',
    content: 'Created via API',
    status: 'publish'
  });

  // Verify creation
  expect(post.id).toBeDefined();
  expect(post.status).toBe('publish');

  // Clean up
  await requestUtils.deleteAllPosts();
});
```

This API reference provides comprehensive documentation for all classes, methods, and utilities available in the `@wordpress/e2e-test-utils-playwright` package, making it easy for LLMs to understand and utilize the testing framework effectively.
