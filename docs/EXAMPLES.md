# WordPress E2E Test Utils Playwright - Examples

## Basic Examples

### 1. Simple Post Creation
```javascript
import { test, expect } from '@wordpress/e2e-test-utils-playwright';

test('Create a simple post', async ({ admin, editor }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  await editor.canvas.locator('role=document[name="Paragraph block"i]').fill('Hello World!');
  await editor.publishPost();
  
  // Verify success message
  await expect(editor.page.locator('.notice-success')).toBeVisible();
});
```

### 2. Page Creation with Multiple Blocks
```javascript
test('Create a page with multiple blocks', async ({ admin, editor }) => {
  await admin.createNewPost({ postType: 'page', title: 'About Us' });
  
  // Add heading
  await editor.insertBlock({
    name: 'core/heading',
    attributes: { content: 'Welcome to Our Company', level: 1 }
  });
  
  // Add paragraph
  await editor.insertBlock({
    name: 'core/paragraph',
    attributes: { content: 'We are a leading company in our field.' }
  });
  
  // Add image
  await editor.insertBlock({
    name: 'core/image',
    attributes: {
      url: 'https://example.com/company-photo.jpg',
      alt: 'Company team photo'
    }
  });
  
  await editor.publishPost();
});
```

### 3. API-Based Content Creation
```javascript
test('Create content via REST API', async ({ requestUtils }) => {
  // Clean up existing content
  await requestUtils.deleteAllPosts();
  
  // Create multiple posts
  const posts = await Promise.all([
    requestUtils.createPost({
      title: 'First Post',
      content: '<!-- wp:paragraph --><p>First post content</p><!-- /wp:paragraph -->',
      status: 'publish'
    }),
    requestUtils.createPost({
      title: 'Second Post',
      content: '<!-- wp:paragraph --><p>Second post content</p><!-- /wp:paragraph -->',
      status: 'draft'
    })
  ]);
  
  expect(posts).toHaveLength(2);
  expect(posts[0].status).toBe('publish');
  expect(posts[1].status).toBe('draft');
});
```

## Intermediate Examples

### 4. Block Editor Interactions
```javascript
test('Complex block editor interactions', async ({ admin, editor, pageUtils }) => {
  await admin.createNewPost();
  
  // Insert a group block with nested content
  await editor.insertBlock({
    name: 'core/group',
    innerBlocks: [
      {
        name: 'core/heading',
        attributes: { content: 'Section Title', level: 2 }
      },
      {
        name: 'core/paragraph',
        attributes: { content: 'Section content goes here.' }
      }
    ]
  });
  
  // Get all blocks and select the group
  const blocks = await editor.getBlocks();
  const groupBlock = blocks.find(block => block.name === 'core/group');
  await editor.selectBlocks(groupBlock.clientId);
  
  // Use keyboard shortcuts
  await pageUtils.pressKeys('primary+c'); // Copy
  await pageUtils.pressKeys('primary+v'); // Paste
  
  // Verify we now have two group blocks
  const updatedBlocks = await editor.getBlocks();
  const groupBlocks = updatedBlocks.filter(block => block.name === 'core/group');
  expect(groupBlocks).toHaveLength(2);
});
```

### 5. Media Upload and Management
```javascript
test('Upload and use media', async ({ admin, editor, requestUtils }) => {
  // Upload media via API
  const media = await requestUtils.uploadMedia('./test-assets/sample-image.jpg');
  
  await admin.createNewPost();
  
  // Insert image block with uploaded media
  await editor.insertBlock({
    name: 'core/image',
    attributes: {
      id: media.id,
      url: media.source_url,
      alt: 'Sample image'
    }
  });
  
  // Verify image is displayed
  const imageBlock = editor.canvas.locator('[data-type="core/image"]');
  await expect(imageBlock.locator('img')).toHaveAttribute('src', media.source_url);
  
  await editor.publishPost();
});
```

### 6. User Permissions Testing
```javascript
test('Test author permissions', async ({ requestUtils }) => {
  // Create an author user
  const author = await requestUtils.createUser({
    username: 'testauthor',
    email: '<EMAIL>',
    password: 'authorpass123',
    roles: ['author']
  });
  
  // Create RequestUtils for the author
  const authorRequestUtils = await RequestUtils.setup({
    user: { username: 'testauthor', password: 'authorpass123' }
  });
  
  // Author should be able to create drafts
  const draft = await authorRequestUtils.createPost({
    title: 'Author Draft',
    content: 'Draft content',
    status: 'draft'
  });
  expect(draft.status).toBe('draft');
  
  // Author should not be able to publish directly (depending on site settings)
  try {
    await authorRequestUtils.createPost({
      title: 'Author Published',
      status: 'publish'
    });
  } catch (error) {
    expect(error.message).toContain('insufficient_permission');
  }
});
```

## Advanced Examples

### 7. Performance Testing
```javascript
test('Monitor page performance', async ({ page, metrics, admin, editor }) => {
  // Initialize performance monitoring
  await metrics.initWebVitals();
  
  // Perform actions that might affect performance
  await admin.createNewPost();
  
  // Add many blocks to test performance
  for (let i = 0; i < 20; i++) {
    await editor.insertBlock({
      name: 'core/paragraph',
      attributes: { content: `Paragraph ${i + 1}` }
    });
  }
  
  // Get performance metrics
  const vitals = await metrics.getWebVitals();
  
  // Assert performance benchmarks
  expect(vitals.LCP).toBeLessThan(2500); // LCP under 2.5s
  expect(vitals.CLS).toBeLessThan(0.1);  // CLS under 0.1
  
  console.log('Performance metrics:', vitals);
});
```

### 8. Lighthouse Auditing
```javascript
test('Run Lighthouse performance audit', async ({ page, lighthouse }) => {
  // Navigate to a published post
  await page.goto('/sample-post/');
  
  // Run Lighthouse audit
  const report = await lighthouse.getReport();
  
  // Check performance metrics
  expect(report.LCP).toBeLessThan(2500);  // Largest Contentful Paint
  expect(report.TTI).toBeLessThan(3800);  // Time to Interactive
  expect(report.TBT).toBeLessThan(300);   // Total Blocking Time
  expect(report.CLS).toBeLessThan(0.1);   // Cumulative Layout Shift
  
  console.log('Lighthouse scores:', report);
});
```

### 9. Site Configuration Testing
```javascript
test('Configure site settings and plugins', async ({ requestUtils, admin }) => {
  // Update site settings
  await requestUtils.updateSiteSettings({
    title: 'Test Site',
    description: 'A site for testing',
    start_of_week: 1, // Monday
    date_format: 'F j, Y',
    time_format: 'g:i a'
  });
  
  // Get available plugins
  const plugins = await requestUtils.getPluginsMap();
  
  // Activate Gutenberg plugin if available
  if (plugins['gutenberg/gutenberg.php']) {
    await requestUtils.activatePlugin('gutenberg');
  }
  
  // Activate a theme
  await requestUtils.activateTheme('twentytwentyfour');
  
  // Verify settings were applied
  const settings = await requestUtils.getSiteSettings();
  expect(settings.title).toBe('Test Site');
  expect(settings.start_of_week).toBe(1);
});
```

### 10. Menu and Navigation Testing
```javascript
test('Create and manage navigation menus', async ({ requestUtils, admin }) => {
  // Create pages for navigation
  const pages = await Promise.all([
    requestUtils.createPage({ title: 'Home', status: 'publish' }),
    requestUtils.createPage({ title: 'About', status: 'publish' }),
    requestUtils.createPage({ title: 'Contact', status: 'publish' })
  ]);
  
  // Create navigation menu
  const menu = await requestUtils.createNavigationMenu({
    title: 'Main Navigation',
    content: `
      <!-- wp:navigation-link {"label":"Home","url":"/"} /-->
      <!-- wp:navigation-link {"label":"About","url":"/about/"} /-->
      <!-- wp:navigation-link {"label":"Contact","url":"/contact/"} /-->
    `
  });
  
  expect(menu.id).toBeDefined();
  expect(menu.title.rendered).toBe('Main Navigation');
  
  // Create classic menu
  const classicMenu = await requestUtils.createClassicMenu({
    name: 'Header Menu',
    locations: ['primary']
  });
  
  expect(classicMenu.term_id).toBeDefined();
});
```

### 11. Custom Block Testing
```javascript
test('Test custom block functionality', async ({ admin, editor }) => {
  await admin.createNewPost();
  
  // Insert custom block (assuming it's registered)
  await editor.insertBlock({
    name: 'my-plugin/testimonial',
    attributes: {
      authorName: 'John Doe',
      testimonialText: 'This is a great product!',
      showAvatar: true
    }
  });
  
  // Interact with custom block in the canvas
  const testimonialBlock = editor.canvas.locator('[data-type="my-plugin/testimonial"]');
  await expect(testimonialBlock).toBeVisible();
  
  // Check that the content is rendered correctly
  await expect(testimonialBlock.locator('.testimonial-text')).toHaveText('This is a great product!');
  await expect(testimonialBlock.locator('.testimonial-author')).toHaveText('John Doe');
  
  // Test block settings in sidebar
  await editor.openDocumentSettingsSidebar();
  const showAvatarToggle = editor.page.locator('.block-editor-block-inspector').getByLabel('Show Avatar');
  await expect(showAvatarToggle).toBeChecked();
  
  // Toggle setting and verify change
  await showAvatarToggle.uncheck();
  await expect(testimonialBlock.locator('.testimonial-avatar')).not.toBeVisible();
});
```

### 12. Accessibility Testing
```javascript
test('Test accessibility compliance', async ({ admin, editor, page }) => {
  await admin.createNewPost();
  
  // Insert image block
  await editor.insertBlock({ name: 'core/image' });
  
  // Click to open media library
  const imageBlock = editor.canvas.locator('[data-type="core/image"]');
  await imageBlock.locator('button').click();
  
  // Upload image
  const fileInput = page.locator('.media-modal input[type="file"]');
  await fileInput.setInputFiles('./test-assets/sample-image.jpg');
  
  // Wait for upload and select image
  await page.waitForSelector('.media-modal .attachment');
  await page.locator('.media-modal .attachment').first().click();
  
  // Verify alt text field is present and required
  const altField = page.locator('.media-modal input[aria-label*="Alt"]');
  await expect(altField).toBeVisible();
  
  // Test that alt text is required for accessibility
  await page.locator('.media-modal .media-button-select').click();
  
  // Should show validation message if alt text is empty
  const validationMessage = page.locator('.media-modal .notice-error');
  await expect(validationMessage).toBeVisible();
  
  // Add alt text and try again
  await altField.fill('A descriptive alt text for the image');
  await page.locator('.media-modal .media-button-select').click();
  
  // Verify image was inserted with alt text
  await expect(imageBlock.locator('img')).toHaveAttribute('alt', 'A descriptive alt text for the image');
});
```

### 13. Multi-site Network Testing
```javascript
test('Test multi-site functionality', async ({ requestUtils }) => {
  // Test main site
  const mainSitePost = await requestUtils.createPost({
    title: 'Main Site Post',
    status: 'publish'
  });
  
  // Switch to different site in network
  const site2RequestUtils = await RequestUtils.setup({
    baseURL: 'http://site2.localhost:8889',
    user: { username: 'admin', password: 'password' }
  });
  
  // Test second site
  const site2Post = await site2RequestUtils.createPost({
    title: 'Site 2 Post',
    status: 'publish'
  });
  
  // Verify posts are separate
  expect(mainSitePost.id).not.toBe(site2Post.id);
  
  // Test site-specific settings
  await site2RequestUtils.updateSiteSettings({
    title: 'Site 2 Title'
  });
  
  const site2Settings = await site2RequestUtils.getSiteSettings();
  expect(site2Settings.title).toBe('Site 2 Title');
});
```

### 14. Interaction Performance Testing
```javascript
test('Measure typing and interaction performance', async ({ admin, editor, metrics }) => {
  await admin.createNewPost();
  await editor.insertBlock({ name: 'core/paragraph' });
  
  // Start performance tracing
  await metrics.startTracing();
  
  // Perform typing interactions
  const paragraph = editor.canvas.locator('role=document[name="Paragraph block"i]');
  await paragraph.type('This is a performance test for measuring typing speed and responsiveness.');
  
  // Stop tracing
  await metrics.stopTracing();
  
  // Analyze performance
  const typingDurations = metrics.getTypingEventDurations();
  const clickDurations = metrics.getClickEventDurations();
  
  // Calculate average typing performance
  const allTypingEvents = typingDurations.flat();
  const avgTypingTime = allTypingEvents.reduce((a, b) => a + b, 0) / allTypingEvents.length;
  
  // Assert performance expectations
  expect(avgTypingTime).toBeLessThan(50); // Average typing event under 50ms
  
  console.log('Average typing performance:', avgTypingTime, 'ms');
  console.log('Total typing events:', allTypingEvents.length);
});
```

### 15. Complete E2E Workflow
```javascript
test('Complete content creation workflow', async ({ requestUtils, admin, editor, page }) => {
  // Setup: Clean environment and create test data
  await requestUtils.deleteAllPosts();
  await requestUtils.deleteAllMedia();
  
  // Upload media for use in post
  const featuredImage = await requestUtils.uploadMedia('./test-assets/featured-image.jpg');
  
  // Create and edit post
  await admin.createNewPost({ title: 'Complete Workflow Test' });
  
  // Add featured image
  await editor.page.locator('[aria-label="Set featured image"]').click();
  await page.locator('.media-modal .attachment').first().click();
  await page.locator('.media-modal .media-button-select').click();
  
  // Add content blocks
  await editor.insertBlock({
    name: 'core/paragraph',
    attributes: { content: 'This is the introduction paragraph.' }
  });
  
  await editor.insertBlock({
    name: 'core/heading',
    attributes: { content: 'Main Section', level: 2 }
  });
  
  await editor.insertBlock({
    name: 'core/list',
    attributes: {
      values: '<li>First item</li><li>Second item</li><li>Third item</li>'
    }
  });
  
  // Save as draft first
  await editor.saveDraft();
  await expect(page.locator('.notice-success')).toContainText('Draft saved');
  
  // Preview the post
  const previewPage = await editor.openPreviewPage();
  await expect(previewPage.locator('h1')).toContainText('Complete Workflow Test');
  await previewPage.close();
  
  // Publish the post
  await editor.publishPost();
  await expect(page.locator('.notice-success')).toContainText('published');
  
  // Verify post exists via API
  const posts = await requestUtils.rest({ path: '/wp/v2/posts' });
  const createdPost = posts.find(post => post.title.rendered === 'Complete Workflow Test');
  expect(createdPost).toBeDefined();
  expect(createdPost.status).toBe('publish');
  expect(createdPost.featured_media).toBe(featuredImage.id);
});
```

These examples demonstrate various testing scenarios from basic post creation to complex workflows, performance testing, and accessibility compliance. Each example is self-contained and can be adapted for specific testing needs.
