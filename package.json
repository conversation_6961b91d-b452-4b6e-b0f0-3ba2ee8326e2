{"name": "@wordpress/e2e-test-utils-playwright", "version": "1.30.0", "description": "End-To-End (E2E) test utils for WordPress.", "author": "The WordPress Contributors", "license": "GPL-2.0-or-later", "keywords": ["wordpress", "<PERSON><PERSON>", "e2e", "utils", "playwright"], "homepage": "https://github.com/WordPress/gutenberg/tree/HEAD/packages/e2e-test-utils-playwright/README.md", "repository": {"type": "git", "url": "https://github.com/WordPress/gutenberg.git", "directory": "packages/e2e-test-utils-playwright"}, "bugs": {"url": "https://github.com/WordPress/gutenberg/issues"}, "engines": {"node": ">=18.12.0", "npm": ">=8.19.2"}, "files": ["build", "build-types"], "main": "./build/index.js", "types": "./build-types", "dependencies": {"change-case": "^4.1.2", "form-data": "^4.0.0", "get-port": "^5.1.1", "lighthouse": "^12.2.2", "mime": "^3.0.0", "web-vitals": "^4.2.1"}, "peerDependencies": {"@playwright/test": ">=1"}, "publishConfig": {"access": "public"}}